global:
  repotype: "public"
  product: corezoid
  serviceMonitor:
    enabled: false
  imagePullPolicy: IfNotPresent
  imageInit:
    repository: hub.corezoid.com/hub.docker.com/library/alpine
    pullPolicy: IfNotPresent
    tag: "3.20.1"
  #######  RabbitMQ  ########
  ## Supported version  3.9.*
  mq:
    internal: false
    ## secret configuration for rabbitmq
    secret:
      ## true - secret will be created automatically with provided values
      ## false - secret should be created manually
      create: true
      name: "rabbitmq-secret"
      data:
        host: "************"
        port: "5672"
        vhost: "/dbcall"
        username: "app-user"
        password: "2cTMc8wioccJdvnSM3ks4IClP9HGUxtm"
  dbcall:
    corezoid_dbcall:
      tag: 2.2.0
      minReplicas: 2
      maxReplicas: 4
      hpa:
        enabled: true
      resources:
        limits:
          cpu: 300m
          memory: 300Mi
        requests:
          cpu: 300m
          memory: 300Mi
      affinity: {}