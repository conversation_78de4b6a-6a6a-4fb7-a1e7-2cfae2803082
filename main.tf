# AWS provider and region
data "aws_caller_identity" "current" {}

data "terraform_remote_state" "app-baseline" {
  backend = "remote"

  config = {
    organization = var.tfe_org_name
    hostname     = var.tfe_host_name

    workspaces = {
      name = var.baseline_workspace_name
    }
  }
}


provider "aws" {
  region = data.terraform_remote_state.app-baseline.outputs.region
}


#
# security groups ids for app load balancers
#
data "aws_security_group" "appstream" {
  name = "pmi-${var.short_name}-appstream-security-group"
}

data "aws_security_group" "alb" {
  filter {
    name   = "tag:Name"
    values = ["${var.short_name}-${var.environment}-default-alb-sg"]
  }
}

# existing aws certificate
data "aws_acm_certificate" "deep" {
  domain   = data.terraform_remote_state.app-baseline.outputs.route53_acn_zone_name
  statuses = ["ISSUED"]
}


output "all_remote_outputs" {
  value = data.terraform_remote_state.app-baseline.outputs
}
