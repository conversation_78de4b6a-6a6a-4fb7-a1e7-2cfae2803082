# Local .terraform directories
**/.terraform/*
**/.terraform~/*
**/.terraform~2/*

# .tfstate files
*.tfstate
*.tfstate.*
.terraform.lock.hcl

# Crash log files
crash.log

# Ignore override files as they are usually used to override resources locally and so
# are not checked in
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
#
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
# example: *tfplan*

# Ignore CLI configuration files
.terraformrc
terraform.rc

# !!! Important. Dont remove this line. backend.tf present and tracked in a git repo may lead to unexpected result on merges between QA/DEV/PRD with TFE
backend.tf
