module "eks" {
  source                   = "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/eks-cluster/aws"
  version                  = "3.0.0"
  cluster_name             = var.cluster_name
  subnet_ids               = data.terraform_remote_state.app-baseline.outputs.eks_subnets_primary
  control_plane_subnet_ids = data.terraform_remote_state.app-baseline.outputs.eks_subnets_primary
  cluster_version          = var.eks_version
  vpc_id                   = local.eks_module_vpc
  aws_region               = local.eks_module_region
  appstream_fleet_role_arn = local.eks_module_appstream
  eks_managed_node_groups  = local.eks_managed_node_groups
  environment              = var.environment
  default_kms_key          = data.terraform_remote_state.app-baseline.outputs.deep_default_kms.eu-west-1.kms_arn
  tags                     = local.common_tags
  map_roles                = var.map_roles
  node_security_group_tags = var.node_security_group_tags
  cluster_addons = {
    "amazon-cloudwatch-observability" = {
      addon_name    = "amazon-cloudwatch-observability"
      addon_version = "v3.1.0-eksbuild.1"
    }
  }
}

module "eks_cluster_autoscaler" {
  source                  = "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/eks-cluster/aws//modules/eks-k8s-cluster-autoscaler" # On Production use the source as "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/eks-cluster/aws//modules/eks-k8s-cluster-autoscaler"
  version                 = "3.0.0"
  aws_region              = local.eks_module_region
  eks_cluster_name        = module.eks.cluster_name
  oidc_provider_arn       = module.eks.oidc_provider_arn
  cluster_oidc_issuer_url = module.eks.cluster_oidc_issuer_url
  tags                    = local.common_tags
  depends_on              = [module.eks]
}

module "external_dns" {
  source                  = "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/eks-cluster/aws//modules/eks-k8s-external-dns" # On Production use the source as "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/eks-cluster/aws//modules/eks-k8s-external-dns"
  version                 = "3.0.0"
  aws_region              = local.eks_module_region
  eks_cluster_name        = module.eks.cluster_name
  oidc_provider_arn       = module.eks.oidc_provider_arn
  cluster_oidc_issuer_url = module.eks.cluster_oidc_issuer_url
  pod_replicas            = var.external_dns_pod_replicas
  txt_owner_id            = module.eks.cluster_name
  tags                    = local.common_tags
  depends_on              = [module.eks]
}

// If you want to destroy all resources at the same time, this alb-ingress will get stuck and you'll get an error.
// Make sure to destroy just this module first with terraform destroy -target
// and then you should be able to destroy everything else.
module "alb_ingress" {
  source                  = "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/eks-cluster/aws//modules/eks-k8s-alb-ingress-controller" # On Production use the source as "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/eks-cluster/aws//modules/eks-k8s-alb-ingress-controller"
  version                 = "3.0.0"
  aws_region              = local.eks_module_region
  vpc_id                  = local.eks_module_vpc
  eks_cluster_name        = module.eks.cluster_name
  oidc_provider_arn       = module.eks.oidc_provider_arn
  cluster_oidc_issuer_url = module.eks.cluster_oidc_issuer_url
  pod_replica_count       = var.alb_ingress_pod_replicas
  environment             = var.environment
  product_code            = data.terraform_remote_state.app-baseline.outputs.account_short_name
  tags                    = local.common_tags
  depends_on              = [module.eks]
}

module "ingress_logging_bucket" {
  source           = "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/eks-cluster/aws//modules/eks-k8s-ingress-logging-bucket" # On Production use the source as "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/eks-cluster/aws//modules/eks-k8s-ingress-logging-bucket"
  version          = "3.0.0"
  aws_region       = local.eks_module_region
  eks_cluster_name = module.eks.cluster_name
  tags             = local.common_tags
}

module "nginx_ingress" {
  source  = "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/eks-cluster/aws//modules/eks-k8s-nginx-ingress-controller" # On Production use the source as "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/eks-cluster/aws//modules/eks-k8s-nginx-ingress-controller"
  version = "3.0.0"
  #count                  = 0 # https://jira.app.pconnect.biz/browse/DEEP-61937 created to investigate how to fix terratest that errors when count !=0
  acm_certificate_arn   = data.aws_acm_certificate.deep.arn
  // "arn:aws:acm:eu-west-1:************:certificate/********-990f-439a-b307-2025284529b3"
  loadbalancer_internal = var.loadbalancer_internal
  #nginx_ingress_chart_repository_url = "https://kubernetes.github.io/ingress-nginx"
  pod_replica_count      = var.nginx_ingress_pod_replicas
  s3_logging_bucket_name = module.ingress_logging_bucket.logging_bucket
  environment            = var.environment
  product_code           = data.terraform_remote_state.app-baseline.outputs.account_short_name
  depends_on             = [module.eks, module.ingress_logging_bucket]
}



module "efs_provisioner" {
  source                  = "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/eks-cluster/aws//modules/eks-k8s-efs-provisioner" # On Production use the source as "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/eks-cluster/aws//modules/eks-k8s-efs-provisioner"
  version                 = "3.0.0"
  volume_name             = var.efs_volume_name
  aws_region              = local.eks_module_region
  vpc_id                  = local.eks_module_vpc
  eks_subnets             = data.terraform_remote_state.app-baseline.outputs.eks_subnets_primary
  eks_security_group_ids  = [module.eks.node_security_group_id, module.eks.cluster_primary_security_group_id]
  pod_replica_count       = var.efs_provisioner_pod_replicas
  efs_throughput_mode     = var.efs_throughput_mode
  efs_throughput          = var.efs_throughput
  eks_cluster_name        = module.eks.cluster_name
  oidc_provider_arn       = module.eks.oidc_provider_arn
  cluster_oidc_issuer_url = module.eks.cluster_oidc_issuer_url
  tags                    = local.common_tags
  depends_on              = [module.eks]
}

module "ebs_provisioner" {
  source                  = "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/eks-cluster/aws//modules/eks-k8s-ebs-provisioner" # On Production use the source as "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/eks-cluster/aws//modules/eks-k8s-ebs-provisioner"
  version                 = "3.0.0"
  cluster_oidc_issuer_url = module.eks.cluster_oidc_issuer_url
  default_kms_key         = data.terraform_remote_state.app-baseline.outputs.deep_default_kms.eu-west-1.kms_arn
  eks_cluster_name        = module.eks.cluster_name
  oidc_provider_arn       = module.eks.oidc_provider_arn
  tags                    = local.common_tags
  depends_on              = [module.eks]
}

module "argocd" {
  source = "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/eks-cluster/aws//modules/eks-k8s-argocd"
  version  = "3.0.0"
  private_subnets_for_alb = local.private_subnets_alb
  pmi_private_ca_arn      = local.pmi_private_ca_arn
  domain_name             = "argocd.${local.route53_acn_pvt_zone_name}"
  redis_ha_enabled        = false
  tags                    = local.common_tags
  depends_on              = [module.eks, module.alb_ingress, module.external_dns]
}

///////////////////variebles//////////////

variable "cluster_name" {
  description = "Kubernetes name"
  type        = string
  default     = "eks-pmi-uz-1"
}

variable "eks_version" {
  description = "Kubernetes `<major>.<minor>` version to use for the EKS cluster (i.e.: `1.25`)."
  type        = string
  default     = "1.31"
}


variable "map_roles" {
  description = "Additional IAM roles to add to the aws-auth configmap."
  type = list(object({
    rolearn  = string
    username = string
    groups   = list(string)
  }))
  //    default = []
  default = [
    {
      rolearn  = "arn:aws:iam::************:role/ucm-ubuntu-DEV20250220125934990200000001"
      username = "eks-user"
      groups   = ["system:masters"]
    },
  ]
}

variable "node_security_group_tags" {
  description = "A map of additional tags to add to the node security group created."
  type        = map(string)
  default = {
    "kubernetes.io/cluster/eks-pmi-uz-1" = null
  }
}

variable "backup_plan" {
  description = "Backup Plan Tag to be added."
  type        = string
  default     = "none"
}

variable "external_dns_pod_replicas" {
  description = "Number of External DNS Controller replicas."
  type        = number
  default     = 2
}


variable "alb_ingress_pod_replicas" {
  description = "Number of ALB Ingress Controller replicas."
  type        = number
  default     = 2
}

variable "loadbalancer_internal" {
  description = "Internal Load Balancer for nginx."
  type        = bool
  default     = false
}


variable "nginx_ingress_pod_replicas" {
  description = "Number of Nginx ingress replicas."
  type        = number
  default     = 2
}

variable "efs_volume_name" {
  description = "EFS Volume Name that will be provisioned."
  type        = string
  default     = "efs1"
}

variable "efs_provisioner_pod_replicas" {
  description = "Number of EFS provisioner replicas."
  type        = number
  default     = 2
}

variable "efs_throughput_mode" {
  description = "The throughput mode could be provisioned or burstable."
  type        = string
  default     = "provisioned"
}

variable "efs_throughput" {
  description = "The throughput, measured in MiB/s, that you want to provision for the file system. Only applicable with `throughput_mode` set to provisioned."
  type        = number
  default     = 5
}
//////////////////////////////////////////////////////////////////////////////////////////////////////////

provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}


provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}

output "cluster_endpoint" {
  description = "Endpoint for EKS control plane."
  value       = module.eks.cluster_endpoint
}
output "eks_cluster_name" {
  description = "EKS Cluster Name from Module."
  value       = module.eks.cluster_name
}

output "node_groups" {
  description = "Outputs from node groups."
  value       = module.eks.eks_managed_node_groups
}
//
data "aws_eks_cluster" "cluster" {
  name = module.eks.cluster_name
  depends_on = [
    module.eks.cluster_id
  ]
}

data "aws_eks_cluster_auth" "cluster" {
  name = module.eks.cluster_name
  depends_on = [
    module.eks.cluster_id
  ]
}