### EC2 Key Pair

# create a new keypair
resource "aws_key_pair" "webserver" {
  key_name   = "PMI-${var.account_id}"
  public_key = var.public_key
}
resource "aws_key_pair" "public_key0" {
  key_name   = "my-key"
  public_key = var.public_key0
}


module "ec2-webserver2" {
  source = "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/ec2-module/aws"
  #  version = "1.5.3"

  ami_id_custom = "ami-0281b2f8d80a8098e"

  name                    = var.webserver2["name"]
  instance_type           = var.webserver2["instance_type"]
  os                      = var.webserver2.os
  disable_api_termination = var.webserver2.disable_api_termination

  product_code       = local.ProductCode
  env                = local.Environment
  backup_plan        = local.Backup_Plan
  maintenance_window = local.MaintenanceWindow

  key_name    = var.public_key3
  kms_key_arn = "arn:aws:kms:eu-west-1:************:key/5f71288e-7f6f-462c-97d6-f09cede1b788"
  #data.terraform_remote_state.app-baseline.outputs.deep_default_kms.eu-west-1.kms_arn

  subnet_id   = data.terraform_remote_state.app-baseline.outputs.private_subnets_primary[0]
  vpc_group   = data.terraform_remote_state.app-baseline.outputs.account_vpc_policy
  allowed_ips = ["10.0.0.0/8"]

  existing_security_groups = [
    data.aws_security_group.appstream.id,
    data.aws_security_group.alb.id,
    aws_security_group.ec2-sg-eks.id
  ]

  platform         = var.webserver2["platform"]
  root_volume_size = var.webserver2["root_volume_size"]

  ebs_volume_device = {
    sdc = {
      device_name           = var.ebs_volume_device.sdc.device_name,
      volume_size           = var.ebs_volume_device.sdc.volume_size,
      volume_type           = var.ebs_volume_device.sdc.volume_type,
      delete_on_termination = var.ebs_volume_device.sdc.delete_on_termination
      kms_key_arn           = "arn:aws:kms:eu-west-1:************:key/aa84deba-4c81-4494-9eae-0bceaaeee434"
      #data.terraform_remote_state.app-baseline.outputs.deep_default_kms.eu-west-1.kms_arn
      backup_plan = local.Backup_Plan
    }
  }

  tags = local.common_tags
}


module "ec2-ubuntu" {
  source = "ptfe-crx5x8zy.deeptpe.pmicloud.xyz/core-prd/ec2-module/aws"

  name          = "${var.short_name}-ubuntu"
  instance_type = "t3.medium"
  os            = "ubuntu20"

  disable_api_termination = var.webserver2.disable_api_termination
  platform                = var.webserver2["platform"]

  product_code       = local.ProductCode
  env                = local.Environment
  backup_plan        = local.Backup_Plan
  maintenance_window = local.MaintenanceWindow

  key_name    = var.ssh_key
  kms_key_arn = data.terraform_remote_state.app-baseline.outputs.deep_default_kms.eu-west-1.kms_arn

  subnet_id   = data.terraform_remote_state.app-baseline.outputs.private_subnets_primary[0]
  vpc_group   = data.terraform_remote_state.app-baseline.outputs.account_vpc_policy
  allowed_ips = ["10.0.0.0/8"]

  existing_security_groups = [
    data.aws_security_group.appstream.id,
    data.aws_security_group.alb.id
  ]

  tags = local.common_tags
}

resource "aws_security_group" "ec2-sg-eks" {
  name        = "ec2-sg-eks"
  description = "ec2-sg-eks"
  vpc_id      = data.terraform_remote_state.app-baseline.outputs.vpc_id_primary
  ingress {
    description = "Allow from EKS to Postgres"
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = ["***********/21"]
  }
  ingress {
    description = "Allow from EKS to RabbitMQ"
    from_port   = 5672
    to_port     = 5672
    protocol    = "tcp"
    cidr_blocks = ["***********/21"]
  }
  ingress {
    description = "Allow from EKS to Redis"
    from_port   = 5379
    to_port     = 6381
    protocol    = "tcp"
    cidr_blocks = ["***********/21"]
  }
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = local.common_tags
}
